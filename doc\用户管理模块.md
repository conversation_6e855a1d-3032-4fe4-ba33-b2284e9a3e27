# 用户管理模块 API 设计文档 (Laravel 10)

本文档详细描述基于 Laravel 10 的用户管理功能模块的 API 接口设计。该系统采用前后端分离架构，用户认证通过第三方系统提供的 token 完成，后端从 token 中提取 `username` 进行用户匹配和登录，本地不存储用户密码，提供安全可靠的用户管理功能。

## 核心要素概览

1. **用户模型**: 定义用户数据的结构和行为，包括用户属性和关联关系
2. **数据库表结构**: `users` 表的详细字段定义，支持用户基本信息存储和第三方认证
3. **API 路由**: 模块提供的所有 API 端点，包括认证接口和用户管理接口
4. **控制器**: 处理 API 请求的核心业务逻辑，实现用户认证和管理功能
5. **表单请求**: API 请求数据的验证规则，确保数据的有效性和安全性
6. **API 资源**: API 响应数据的格式化和转换，提供统一的数据结构
7. **认证机制**: 第三方 Token 登录流程及本地 Sanctum Token 颁发与使用，实现无缝认证
8. **响应格式**: 统一的 API 响应格式，包含 code、message 和 data 字段

---

## 1. 用户模型

### 1.1 User 模型

* **类名:** `App\Models\User\User`
* **继承自:** `Illuminate\Foundation\Auth\User as Authenticatable`
* **使用的 Traits:**
  * `Laravel\Sanctum\HasApiTokens`: 用于支持 API token认证
  * `Illuminate\Database\Eloquent\Factories\HasFactory`: 用于测试数据填充
  * `Illuminate\Notifications\Notifiable`: 用于支持通知功能
  * `Spatie\Permission\Traits\HasRoles`: 用于支持角色和权限管理
* **核心属性:**
  * `id` (`BIGINT UNSIGNED`, `PK`, `AI`): 用户唯一标识符
  * `username` (`VARCHAR`, `Unique`): 用户名，从第三方 Token 获取，是用户登录和识别的唯一业务主键
  * `nickname` (`VARCHAR`, `Nullable`): 用户显示名称/昵称
  * `is_active` (`BOOLEAN`, `Default: true`): 账户是否激活状态，`false` 表示账户被禁用
  * `third_party_user_id` (`VARCHAR`, `Nullable`, `Index`): 可选字段，用于存储第三方系统中的用户唯一 ID（如果它与 `username` 不同）
  * `last_login_at` (`TIMESTAMP`, `Nullable`): 用户最后登录时间
  * `created_at` (`TIMESTAMP`): 记录创建时间
  * `updated_at` (`TIMESTAMP`): 记录更新时间
* **不包含的属性:**
  * `password`: 由于通过第三方登录，本地不需要密码
* **可批量赋值:**
  * `username`, `nickname`, `is_active`, `third_party_user_id`, `last_login_at`
* **属性类型转换:**
  * `is_active` => `boolean`
  * `last_login_at` => `datetime:Y-m-d H:i:s`
  * `created_at` => `datetime:Y-m-d H:i:s`
  * `updated_at` => `datetime:Y-m-d H:i:s`
* **关联关系:**
  * `roles()`: 多对多关联，用户拥有的角色
  * `permissions()`: 多对多关联，用户直接拥有的权限（通过HasRoles特性提供）

---

## 2. 数据库表结构

### 2.1 用户表 (users)

* **表名:** `users`
* **通过迁移创建:** `php artisan make:migration create_users_table`
* **数据库连接:** `new_package_tool`
* **Schema 定义:**

| 字段名                | 类型                 | 约束/属性                                 | 描述                                      |
| :-------------------- | :------------------- | :---------------------------------------- | :---------------------------------------- |
| `id`                  | `bigIncrements`      | `PRIMARY KEY`, `UNSIGNED`                 | 自增主键 ID                               |
| `username`            | `string`             | `UNIQUE`, `NOT NULL`                      | 用户名 (来自第三方，业务唯一键)             |
| `nickname`            | `string`             | `NULLABLE`                                | 用户昵称/姓名                             |
| `is_active`           | `boolean`            | `NOT NULL`, `DEFAULT true`                | 账户是否激活                              |
| `third_party_user_id` | `string`             | `NULLABLE`, `INDEX`                       | (可选) 第三方系统用户 ID                  |
| `last_login_at`       | `timestamp`          | `NULLABLE`                                | 最后登录时间                              |
| `created_at`          | `timestamp`          | `NULLABLE`                                | 创建时间                                  |
| `updated_at`          | `timestamp`          | `NULLABLE`                                | 更新时间                                  |

* **索引:**
  * `username`: 唯一索引，确保用户名唯一性
  * `third_party_user_id`: 普通索引，优化第三方ID查询

---

## 3. API 路由

* **定义文件:** `routes/api.php`
* **路由前缀:** 所有路由自动带有 `/api` 前缀
* **路由命名空间:**
  * 认证控制器: `App\Http\Controllers\Auth`
  * 用户控制器: `App\Http\Controllers\User`

### 3.1 公开路由 (无需认证)

| 方法   | URI          | 控制器方法                | 描述                                                                                                |
| :----- | :----------- | :------------------------ | :-------------------------------------------------------------------------------------------------- |
| `POST` | `/login`     | `AuthController@login`    | 处理用户通过第三方 Token 的登录请求，返回 Sanctum Token 和用户信息                                  |

### 3.2 保护路由 (需要 Sanctum Token 认证)

这些路由需要请求头中包含有效的 `Authorization: Bearer <YourSanctumToken>`。

#### 认证相关路由

| 方法   | URI          | 控制器方法                | 描述                                                                                                |
| :----- | :----------- | :------------------------ | :-------------------------------------------------------------------------------------------------- |
| `GET`  | `/user`      | `AuthController@me`       | 获取当前已认证用户的详细信息                                                                        |
| `POST` | `/logout`    | `AuthController@logout`   | 使当前认证用户持有的 Sanctum API Token 失效，实现登出                                               |

#### 用户管理路由

| 方法     | URI                | 控制器方法            | 描述                                                                                                | 权限要求         |
| :------- | :----------------- | :-------------------- | :-------------------------------------------------------------------------------------------------- | :-------------- |
| `GET`    | `/users`           | `UserController@index`   | 获取用户列表（分页），支持筛选和排序                                                               | `user.list`     |
| `POST`   | `/users`           | `UserController@store`   | 创建新用户，主要用于管理员手动添加用户                                                             | `user.create`   |
| `GET`    | `/users/{user}`    | `UserController@show`    | 获取指定用户的详细信息                                                                             | `user.view`     |
| `PUT`    | `/users/{user}`    | `UserController@update`  | 更新指定用户的信息，`username` 通常不允许更新                                                      | `user.update`   |
| `DELETE` | `/users/{user}`    | `UserController@destroy` | 删除指定用户                                                                                       | `user.delete`   |

#### 用户角色管理路由

| 方法     | URI                       | 控制器方法                      | 描述                                | 权限要求         |
| :------- | :------------------------ | :------------------------------ | :---------------------------------- | :-------------- |
| `GET`    | `/users/{user}/roles`     | `UserRoleController@index`      | 获取指定用户的角色列表              | `userRole.view`  |
| `POST`   | `/users/{user}/roles`     | `UserRoleController@store`      | 为指定用户分配角色                  | `userRole.assign` |
| `POST`   | `/users/{user}/check-permission` | `UserRoleController@checkPermission` | 检查用户是否拥有指定权限  | `userRole.view`  |

#### 路由注册代码

```php
// 公开路由 (无需认证)
Route::post('/login', [AuthController::class, 'login']);

// 保护路由 (需要 Sanctum Token 认证)
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', [AuthController::class, 'me']);
    Route::post('/logout', [AuthController::class, 'logout']);

    // 用户管理接口 (User Management API Resource)
    Route::apiResource('users', UserController::class);

    // 用户角色管理
    Route::get('users/{user}/roles', [UserRoleController::class, 'index']);
    Route::post('users/{user}/roles', [UserRoleController::class, 'store']);
    Route::post('users/{user}/check-permission', [UserRoleController::class, 'checkPermission']);
});
```

---

## 4. 控制器

### 4.1 认证控制器 (AuthController)

* **类名:** `App\Http\Controllers\Auth\AuthController`
* **主要职责:** 处理用户认证相关的请求，包括登录、获取当前用户信息和登出

#### login(Request $request)

处理用户通过第三方Token登录的请求。

**实现逻辑:**
1. 验证请求中是否包含 `third_party_token`
2. 调用第三方服务验证Token并获取用户信息（如 `username`）
3. 在本地数据库中查找用户:
   * 如果用户存在且激活，则更新 `last_login_at` 和其他信息（如 `nickname`、`avatar`）
   * 如果用户存在但未激活，则返回错误
   * 如果用户不存在，则返回用户不存在错误
4. 为成功识别的本地用户生成一个 Sanctum API Token
5. 返回包含Token和用户数据的JSON响应
6. 处理各种错误情况（如Token无效、用户被禁用、用户不存在等）

**响应示例:**
```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "token": "YOUR_SANCTUM_TOKEN",
    "user": {
      "id": 1,
      "username": "user_example",
      "nickname": "示例用户",
      "is_active": true,
      "third_party_user_id": "tp_12345",
      "last_login_at": "2023-05-16 12:00:00",
      "created_at": "2023-05-16 10:00:00",
      "updated_at": "2023-05-16 12:00:00"
    }
  }
}
```

#### me(Request $request)

获取当前已认证用户的信息。

**实现逻辑:**
1. 获取当前已认证用户的实例
2. 使用 `UserResource` 格式化用户信息并返回

**响应示例:**
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "user_example",
    "nickname": "示例用户",
    "is_active": true,
    "third_party_user_id": "tp_12345",
    "last_login_at": "2023-05-16 12:00:00",
    "created_at": "2023-05-16 10:00:00",
    "updated_at": "2023-05-16 12:00:00"
  }
}
```

#### logout(Request $request)

使当前用户的Token失效，实现登出。

**实现逻辑:**
1. 使当前用户的当前 Sanctum Token 失效
2. 返回成功响应

**响应示例:**
```json
{
  "code": 0,
  "message": "登出成功",
  "data": null
}
```

### 4.2 用户控制器 (UserController)

* **类名:** `App\Http\Controllers\User\UserController`
* **主要职责:** 处理用户管理相关的CRUD操作
* **权限前缀:** `user`
* **主要方法:**

#### index(Request $request)

获取用户列表，支持搜索、筛选和排序。

**实现逻辑:**
1. 根据请求参数构建查询:
   * 支持通过 `keyword` 搜索 `username`, `nickname` 和 `third_party_user_id`
   * 支持通过 `is_active` 筛选激活/未激活用户
   * 支持通过 `sort_by` 和 `sort_dir` 参数排序
2. 分页获取用户列表
3. 使用 `UserCollection` 格式化分页结果并返回
4. 实现权限控制，通常仅管理员可操作

**响应示例:**
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "username": "user_example",
        "nickname": "示例用户",
        "is_active": true,
        "third_party_user_id": "tp_12345",
        "last_login_at": "2023-05-16 12:00:00",
        "created_at": "2023-05-16 10:00:00",
        "updated_at": "2023-05-16 12:00:00"
      },
      // 更多用户...
    ],
    "meta": {
      "total": 10,
      "per_page": 15,
      "current_page": 1,
      "last_page": 1,
      "from": 1,
      "to": 10
    }
  }
}
```

#### store(StoreUserRequest $request)

创建新用户。

**实现逻辑:**
1. 验证请求数据（通过 `StoreUserRequest`）
2. 创建新用户记录
3. 使用 `UserResource` 格式化新用户信息并返回，状态码为201
4. 实现权限控制，通常仅管理员可操作

**响应示例:**
```json
{
  "code": 0,
  "message": "用户创建成功",
  "data": {
    "id": 11,
    "username": "new_user",
    "nickname": "新用户",
    "is_active": true,
    "third_party_user_id": "tp_67890",
    "last_login_at": null,
    "created_at": "2023-05-16 14:00:00",
    "updated_at": "2023-05-16 14:00:00"
  }
}
```

#### show(User $user)

获取指定用户的详细信息。

**实现逻辑:**
1. 利用路由模型绑定自动注入 `User` 实例
2. 使用 `UserResource` 格式化用户信息并返回
3. 实现权限控制，如管理员或用户本人可查看

**响应示例:**
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "user_example",
    "nickname": "示例用户",
    "is_active": true,
    "third_party_user_id": "tp_12345",
    "last_login_at": "2023-05-16 12:00:00",
    "created_at": "2023-05-16 10:00:00",
    "updated_at": "2023-05-16 12:00:00"
  }
}
```

#### update(UpdateUserRequest $request, User $user)

更新指定用户的信息。

**实现逻辑:**
1. 验证请求数据（通过 `UpdateUserRequest`）
2. 更新用户记录
3. 使用 `UserResource` 格式化更新后的用户信息并返回
4. 实现权限控制，如管理员或用户本人可更新

**响应示例:**
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "user_example",
    "nickname": "更新后的昵称",
    "is_active": true,
    "third_party_user_id": "tp_12345",
    "last_login_at": "2023-05-16 12:00:00",
    "created_at": "2023-05-16 10:00:00",
    "updated_at": "2023-05-16 15:00:00"
  }
}
```

#### destroy(User $user)

删除指定用户。

**实现逻辑:**
1. 删除指定用户
2. 返回成功响应
3. 实现权限控制，通常仅管理员可操作

**响应示例:**
```json
{
  "code": 0,
  "message": "用户删除成功",
  "data": null
}
```

### 4.3 用户角色控制器 (UserRoleController)

* **类名:** `App\Http\Controllers\User\UserRoleController`
* **主要职责:** 处理用户角色的分配和查询等操作
* **权限前缀:** `userRole`
* **标准资源方法的权限映射:**
  * `index` => `view`
  * `store` => `assign`
* **额外方法的权限映射:**
  * `checkPermission` => `view`
* **主要方法:**

#### index(User $user)

获取指定用户的角色列表。

**实现逻辑:**
1. 获取用户的角色
2. 使用 `RoleResource` 格式化角色集合并返回
3. 实现权限控制，需要 `userRole.view` 权限

**响应示例:**
```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "admin",
      "display_name": "管理员",
      "description": "系统管理员",
      "is_active": true,
      "created_at": "2023-05-16 10:00:00",
      "updated_at": "2023-05-16 10:00:00"
    },
    // 更多角色...
  ]
}
```

#### store(Request $request, User $user)

为指定用户分配角色。

**实现逻辑:**
1. 验证请求数据，确保 `role_ids` 是有效的角色ID数组
2. 使用事务确保数据一致性
3. 获取角色并同步到用户
4. 清除权限缓存
5. 返回成功响应
6. 实现权限控制，需要 `userRole.assign` 权限

**响应示例:**
```json
{
  "code": 0,
  "message": "角色分配成功",
  "data": null
}
```

#### checkPermission(Request $request, User $user)

检查用户是否拥有指定权限。

**实现逻辑:**
1. 验证请求数据，确保 `permission` 是有效的权限名称
2. 检查用户是否拥有指定权限
3. 返回检查结果
4. 实现权限控制，需要 `userRole.view` 权限

**响应示例:**
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "has_permission": true
  }
}
```

---

## 5. 表单请求 (Form Requests)

用于封装验证逻辑，使控制器保持简洁。通过 `php artisan make:request <RequestName>` 创建。

### 5.1 `StoreUserRequest` (用于 `POST /users`)

* **`authorize()` 方法:** 定义谁有权限执行此创建请求（例如，仅限管理员）。
* **`rules()` 方法 (示例):**
    ```php
    [
        'username' => ['required', 'string', 'max:255', Rule::unique('users', 'username')],
        'nickname' => ['nullable', 'string', 'max:255'],
        'is_active' => ['sometimes', 'boolean'],
        'third_party_user_id' => ['nullable', 'string', 'max:255', Rule::unique('users', 'third_party_user_id')], // 如果使用
    ]
    ```

### 5.2 `UpdateUserRequest` (用于 `PUT /users/{user}`)

* **`authorize()` 方法:** 定义谁有权限执行此更新请求（例如，管理员或用户本人更新自己的信息）。
* **`rules()` 方法 (示例):**
    ```php
    [
        // 'username' => ['sometimes', 'string', 'max:255', Rule::unique('users')->ignore($this->route('user')->id)], // 通常不建议更新
        'nickname' => ['sometimes', 'nullable', 'string', 'max:255'],
        'is_active' => ['sometimes', 'boolean'],
    ]
    ```

---

## 6. API 资源 (API Resources)

用于将 Eloquent 模型和集合转换为结构一致的 JSON 响应。通过 `php artisan make:resource <ResourceName>` 创建。

### 6.1 `UserResource` (`App\Http\Resources\User\UserResource`)

转换单个 `User` 模型实例。

* **继承自:** `App\Http\Resources\Base\BaseJsonResource`
* **`toArray($request)` 方法 (示例结构):**
    ```php
    [
        'id' => $this->id,
        'username' => $this->username,
        'nickname' => $this->nickname,
        'is_active' => (bool) $this->is_active,
        'third_party_user_id' => $this->third_party_user_id,
        'last_login_at' => $this->last_login_at ? $this->last_login_at->format('Y-m-d H:i:s') : null,
        'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
    ]
    ```

### 6.2 `UserCollection` (`App\Http\Resources\User\UserCollection`)

转换 `User` 模型集合（通常用于分页列表）。

* **继承自:** `App\Http\Resources\Base\ListResourceCollection`
* **`$collects = UserResource::class;`**: 指定集合中每个元素都由 `UserResource` 处理。
* **响应格式:**
    ```json
    {
        "code": 0,
        "message": "操作成功",
        "data": {
            "list": [ /* 用户列表数据 */ ],
            "meta": { /* 分页元数据，不包含links和path */ }
        }
    }
    ```

---

## 7. 认证机制 (Authentication)

### 7.1 第三方 Token 登录流程

1.  **客户端认证:** 前端应用引导用户在第三方认证系统完成身份验证，并获取一个 `third_party_token`。
2.  **提交 Token 至后端:** 客户端将此 `third_party_token` 通过 HTTP POST 请求发送到后端的 `/api/login` 端点。
3.  **后端 Token 验证与用户处理:**
    * `AuthController@login` 方法接收该 Token。
    * 后端调用一个专门的**第三方认证服务** (ThirdPartyAuthService) 来验证此 `third_party_token` 的有效性。此服务负责与第三方系统交互（例如，调用其 Token 校验接口，或解码并验证 JWT 签名）。
    * 若 Token 有效，该服务从中提取用户的唯一 `username` 以及其他可选信息 (`nickname`, `avatar`)。
    * 后端根据 `username` 在本地 `users` 数据库中查找用户。
        * **用户存在且激活:** 更新用户信息（如昵称、头像、最后登录时间），然后登录成功。
        * **用户不存在:** 返回用户不存在错误。
        * **用户存在但未激活:** 登录失败，返回相应错误。
4.  **颁发 Sanctum API Token:** 用户在本地成功识别后，Laravel Sanctum 会为此用户生成一个新的、与设备/会话相关的 API Token。
5.  **返回 Sanctum Token:** 后端将此 Sanctum API Token (通常是 Bearer Token) 连同用户信息一并返回给客户端。

### 7.2 后续 API 请求认证

* 客户端在后续访问受保护的 API 端点时，必须在 HTTP 请求的 `Authorization` 头部包含此前获取的 Sanctum API Token。
    * 格式: `Authorization: Bearer <SanctumToken>`
* Laravel 的 `auth:sanctum` 中间件会拦截这些请求，验证 Token 的有效性，并为当前请求认证对应的用户。

### 7.3 登出机制

* 客户端向 `POST /api/logout` 端点发送请求（携带有效的 Sanctum Token）。
* 后端 `AuthController@logout` 方法会使当前请求使用的 Sanctum Token 失效。
* **注意:** 此操作仅使后端 API 的会话失效，并不会使用户从第三方认证系统中登出。

---

## 8. 统一响应格式

所有API响应都遵循统一的格式，包含code、message和data三个字段：

```json
{
  "code": 0,       // 业务状态码，0表示成功，其他值表示错误
  "message": "操作成功", // 业务消息，支持国际化
  "data": { ... }    // 业务数据，可能为对象、数组或null
}
```

### 8.1 成功响应

成功响应使用code=0，HTTP状态码通常为200（创建资源时为201）：

```json
{
  "code": 0,
  "message": "操作成功",
  "data": { ... } // 或 null
}
```

### 8.2 错误响应

错误响应使用对应的错误码，HTTP状态码与业务状态码一致：

```json
{
  "code": 404,
  "message": "用户不存在",
  "data": null
}
```

### 8.3 常见状态码

| HTTP状态码 | 业务状态码 | 说明 |
| :--------- | :--------- | :--- |
| 200        | 0          | 请求成功 |
| 201        | 0          | 资源创建成功 |
| 400        | 400        | 请求参数错误 |
| 401        | 401        | 认证失败，Token无效或已过期 |
| 403        | 403        | 权限不足，无权访问该资源 |
| 404        | 404        | 资源不存在 |
| 422        | 422        | 请求参数验证失败 |
| 500        | 500        | 服务器内部错误 |

## 9. 第三方登录逻辑 (Third-Party Login Logic - `AuthController@login` 详解)

此逻辑是连接第三方认证和本地用户系统的核心。项目中已将第三方交互部分抽象为一个独立的服务类 `App\Services\Auth\ThirdPartyAuthService`。

**`ThirdPartyAuthServiceInterface` 接口:**

```php
<?php

namespace App\Services\Auth;

interface ThirdPartyAuthServiceInterface
{
    /**
     * 根据第三方 Token 获取用户信息。
     * @param string $thirdPartyToken
     * @return array|null 包含用户信息的数组 (如 ['username' => ..., 'nickname' => ..., 'avatar' => ...])，或在 Token 无效时返回 null。
     */
    public function getUserInfoFromToken(string $thirdPartyToken): ?array;
}