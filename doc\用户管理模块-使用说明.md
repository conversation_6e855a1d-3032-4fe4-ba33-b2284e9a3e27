# 用户管理模块 API 使用说明

## 概述

本文档提供用户管理模块API的详细使用说明，包括认证流程和用户管理接口的调用方法。该系统采用前后端分离架构，用户认证通过第三方系统提供的token完成，后端从token中提取`username`进行用户匹配和登录，本地不存储用户密码。

## 环境要求

- PHP 8.1+
- Laravel 10+
- MySQL 5.7+ / 8.0+
- Composer 2.0+

## 认证流程

### 1. 登录流程

1. 用户在第三方系统完成身份验证，获取`third_party_token`
2. 前端将`third_party_token`发送到后端的`/api/login`接口
3. 后端验证token并查找用户
   - 如果用户存在且激活，则更新用户信息并登录成功
   - 如果用户不存在，则返回用户不存在错误
   - 如果用户存在但未激活，则登录失败
4. 返回Sanctum Token和用户信息
5. 前端存储Sanctum Token用于后续请求

### 2. API认证

所有受保护的API请求都需要在请求头中包含Sanctum Token：

```
Authorization: Bearer YOUR_SANCTUM_TOKEN
```

### 3. 登出

调用`/api/logout`接口使当前Sanctum Token失效。

**注意**：此操作仅使后端API的会话失效，不会使用户从第三方认证系统中登出。

## API接口说明

### 认证接口

#### 登录

- **URL**: `/api/login`
- **方法**: `POST`
- **认证**: 不需要
- **请求体**:
  ```json
  {
    "third_party_token": "YOUR_THIRD_PARTY_TOKEN"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 0,
    "message": "登录成功",
    "data": {
      "token": "YOUR_SANCTUM_TOKEN",
      "user": {
        "id": 1,
        "username": "user_example",
        "nickname": "示例用户",
        "is_active": true,
        "third_party_user_id": "tp_12345",
        "last_login_at": "2023-05-16 12:00:00",
        "created_at": "2023-05-16 10:00:00",
        "updated_at": "2023-05-16 12:00:00"
      }
    }
  }
  ```
- **错误响应** (401):
  ```json
  {
    "code": 401,
    "message": "第三方Token无效",
    "data": null
  }
  ```
- **错误响应** (403):
  ```json
  {
    "code": 403,
    "message": "用户已被禁用",
    "data": null
  }
  ```
- **错误响应** (404):
  ```json
  {
    "code": 404,
    "message": "用户不存在",
    "data": null
  }
  ```

#### 获取当前用户信息

- **URL**: `/api/user`
- **方法**: `GET`
- **认证**: 需要
- **成功响应** (200):
  ```json
  {
    "code": 0,
    "message": "操作成功",
    "data": {
      "id": 1,
      "username": "user_example",
      "nickname": "示例用户",
      "is_active": true,
      "third_party_user_id": "tp_12345",
      "last_login_at": "2023-05-16 12:00:00",
      "created_at": "2023-05-16 10:00:00",
      "updated_at": "2023-05-16 12:00:00"
    }
  }
  ```

#### 登出

- **URL**: `/api/logout`
- **方法**: `POST`
- **认证**: 需要
- **成功响应** (200):
  ```json
  {
    "code": 0,
    "message": "登出成功",
    "data": null
  }
  ```

### 用户管理接口

#### 获取用户列表

- **URL**: `/api/users`
- **方法**: `GET`
- **认证**: 需要
- **权限**: 通常为管理员
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `per_page`: 每页记录数 (默认: 15)
  - `keyword`: 搜索关键词，用于搜索username、nickname和third_party_user_id
  - `sort_by`: 排序字段 (默认: created_at)
  - `sort_dir`: 排序方向 (asc, desc) (默认: desc)
  - `is_active`: 是否只显示激活用户 (true, false)
- **成功响应** (200):
  ```json
  {
    "code": 0,
    "message": "操作成功",
    "data": {
      "list": [
        {
          "id": 1,
          "username": "user_example",
          "nickname": "示例用户",
          "is_active": true,
          "third_party_user_id": "tp_12345",
          "last_login_at": "2023-05-16 12:00:00",
          "created_at": "2023-05-16 10:00:00",
          "updated_at": "2023-05-16 12:00:00"
        },
        {
          "id": 2,
          "username": "another_user",
          "nickname": "另一个用户",
          "is_active": true,
          "third_party_user_id": "tp_67890",
          "last_login_at": "2023-05-16 11:00:00",
          "created_at": "2023-05-16 09:00:00",
          "updated_at": "2023-05-16 11:00:00"
        }
      ],
      "meta": {
        "total": 2,
        "per_page": 15,
        "current_page": 1,
        "last_page": 1,
        "from": 1,
        "to": 2
      }
    }
  }
  ```

#### 创建用户

- **URL**: `/api/users`
- **方法**: `POST`
- **认证**: 需要
- **权限**: 通常为管理员
- **请求体**:
  ```json
  {
    "username": "new_user",
    "nickname": "新用户",
    "is_active": true,
    "third_party_user_id": "tp_12345"
  }
  ```
- **成功响应** (201):
  ```json
  {
    "code": 0,
    "message": "用户创建成功",
    "data": {
      "id": 3,
      "username": "new_user",
      "nickname": "新用户",
      "is_active": true,
      "third_party_user_id": "tp_12345",
      "last_login_at": null,
      "created_at": "2023-05-16 14:30:00",
      "updated_at": "2023-05-16 14:30:00"
    }
  }
  ```
- **错误响应** (422):
  ```json
  {
    "code": 422,
    "message": "用户名已存在",
    "data": null
  }
  ```

#### 获取指定用户

- **URL**: `/api/users/{id}`
- **方法**: `GET`
- **认证**: 需要
- **权限**: 管理员或用户本人
- **成功响应** (200):
  ```json
  {
    "code": 0,
    "message": "操作成功",
    "data": {
      "id": 1,
      "username": "user_example",
      "nickname": "示例用户",
      "is_active": true,
      "third_party_user_id": "tp_12345",
      "last_login_at": "2023-05-16 12:00:00",
      "created_at": "2023-05-16 10:00:00",
      "updated_at": "2023-05-16 12:00:00"
    }
  }
  ```
- **错误响应** (404):
  ```json
  {
    "code": 404,
    "message": "用户不存在",
    "data": null
  }
  ```

#### 更新用户

- **URL**: `/api/users/{id}`
- **方法**: `PUT`
- **认证**: 需要
- **权限**: 管理员或用户本人
- **请求体**:
  ```json
  {
    "nickname": "更新后的昵称",
    "is_active": true
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 0,
    "message": "操作成功",
    "data": {
      "id": 1,
      "username": "user_example",
      "nickname": "更新后的昵称",
      "is_active": true,
      "third_party_user_id": "tp_12345",
      "last_login_at": "2023-05-16 12:00:00",
      "created_at": "2023-05-16 10:00:00",
      "updated_at": "2023-05-16 15:00:00"
    }
  }
  ```

#### 删除用户

- **URL**: `/api/users/{id}`
- **方法**: `DELETE`
- **认证**: 需要
- **权限**: 通常为管理员
- **成功响应** (200):
  ```json
  {
    "code": 0,
    "message": "用户删除成功",
    "data": null
  }
  ```

## 开发与测试工具

### Postman测试集合

项目包含一个Postman测试集合文件 `doc/user-management-api-postman-collection.json`，可以导入到Postman中进行API测试。

#### 使用步骤

1. 导入集合到Postman
2. 设置环境变量:
   - `base_url`: API基础URL (例如: http://localhost:8000)
   - `sanctum_token`: 登录后获取的Sanctum Token
3. 首先调用登录接口获取token
4. 登录成功后，从响应中复制token值并更新环境变量`sanctum_token`
5. 现在可以测试其他需要认证的接口了

### 前端集成示例

#### 登录示例 (Vue.js + Axios)

```javascript
// 登录函数
async function login(thirdPartyToken) {
  try {
    const response = await axios.post('/api/login', {
      third_party_token: thirdPartyToken
    });

    // 存储token到localStorage
    localStorage.setItem('token', response.data.data.token);

    // 存储用户信息到Vuex store
    store.commit('setUser', response.data.data.user);

    return response.data.data;
  } catch (error) {
    console.error('登录失败:', error.response?.data?.message || error.message);
    throw error;
  }
}

// 设置请求拦截器添加token
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

## 错误处理与状态码

### 响应格式

所有API响应都遵循统一的格式:

```json
{
  "code": 200,       // 业务状态码
  "message": "操作成功", // 业务消息
  "data": { ... }    // 业务数据，可能为对象、数组或null
}
```

### 常见状态码

| HTTP状态码 | 业务状态码 | 说明 |
| :--------- | :--------- | :--- |
| 200        | 0          | 请求成功 |
| 201        | 0          | 资源创建成功 |
| 400        | 400        | 请求参数错误 |
| 401        | 401        | 认证失败，Token无效或已过期 |
| 403        | 403        | 权限不足，无权访问该资源 |
| 404        | 404        | 资源不存在 |
| 422        | 422        | 请求参数验证失败 |
| 500        | 500        | 服务器内部错误 |

### 表单验证错误

当请求参数验证失败时，返回422状态码，并在message中返回第一条错误信息：

```json
{
  "code": 422,
  "message": "用户名已存在",
  "data": null
}
```

## 权限控制

用户管理模块的API接口需要不同级别的权限才能访问：

1. **公开接口**：无需认证即可访问
   - `/api/login`：用于用户登录

2. **需要认证的接口**：需要有效的Sanctum Token
   - `/api/user`：获取当前用户信息
   - `/api/logout`：用户登出

3. **需要管理员权限的接口**：
   - `/api/users` (GET)：获取用户列表
   - `/api/users` (POST)：创建新用户
   - `/api/users/{id}` (DELETE)：删除用户

4. **需要管理员或用户本人权限的接口**：
   - `/api/users/{id}` (GET)：查看用户详情
   - `/api/users/{id}` (PUT)：更新用户信息

> 注意：权限控制的具体实现将在权限管理模块中完成，目前用户管理模块的权限控制仅为预留设计。

## 最佳实践

1. **安全性**：
   - 始终通过HTTPS传输数据
   - 不要在客户端存储敏感信息
   - Token过期后及时清理

2. **性能优化**：
   - 合理使用分页参数，避免获取过多数据
   - 使用适当的缓存策略

3. **错误处理**：
   - 在前端优雅处理API错误
   - 实现全局错误拦截器
   - 为用户提供友好的错误提示
