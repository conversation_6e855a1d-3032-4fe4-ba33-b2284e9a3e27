# 大盘模块 API 文档

> 作者：陈建权
>
> 日期：2025-01-15
>
> 版本：1.0.0

## 简介

大盘模块提供 SDK 监控数据的综合展示功能，整合版本数据、异常数据、性能数据和 SDK 比率数据，为开发者提供全面的监控大盘。该模块支持缓存机制，提高响应性能。

## API 基础信息

- **基础URL**: `/api`
- **环境**:
  - **正式环境**: `https://sdk-monitor-dashboard.shiyue.com/api`
- **认证方式**: 无需认证
- **响应格式**: JSON
- **统一响应结构**:
  ```json
  {
    "code": 0,       // 业务状态码，0表示成功，其他值表示错误
    "message": "操作成功", // 业务消息，支持国际化
    "data": { ... }    // 业务数据，可能为对象、数组或null
  }
  ```

## API 接口列表

### 获取大盘数据

> 获取 SDK 监控大盘的综合数据，包括版本分析、异常统计、性能指标和关键比率数据

- **接口URL**: `/dashboard`
- **请求方式**: GET
- **是否需要认证**: 否

#### 请求参数

| 参数名           | 类型    | 必填 | 描述                                    |
| :--------------- | :------ | :--- | :-------------------------------------- |
| developer_app_id | integer | 是   | 开发者应用ID，必须大于0                 |
| start_time       | string  | 是   | 开始时间，格式：Y-m-d H:i:s             |
| end_time         | string  | 是   | 结束时间，格式：Y-m-d H:i:s，必须晚于开始时间 |

#### 响应参数

| 参数名         | 类型   | 描述                     |
| :------------- | :----- | :----------------------- |
| code           | integer| 业务状态码，0表示成功     |
| message        | string | 业务消息，支持国际化      |
| data           | object | 业务数据                 |
| data.inner_version | array | 内部版本数据数组        |
| data.inner_version[].version | string | 内部版本号（时间戳格式） |
| data.inner_version[].app_launch_error_rate | string | 应用启动错误率 |
| data.inner_version[].crash_rate | string | 崩溃率 |
| data.inner_version[].error_rate | string | 错误率 |
| data.inner_version[].smoothness | string | 卡顿率 |
| data.app_version | array | 应用版本数据数组        |
| data.app_version[].version | string | 应用版本号（语义化版本） |
| data.app_version[].app_launch_error_rate | string | 应用启动错误率 |
| data.app_version[].crash_rate | string | 崩溃率 |
| data.app_version[].error_rate | string | 错误率 |
| data.app_version[].smoothness | string | 卡顿率 |
| data.hit_bug_data | object | 异常监控数据           |
| data.hit_bug_data.crash | string | 崩溃率 |
| data.hit_bug_data.error | string | 错误率 |
| data.hit_bug_data.start_count | string | 启动次数 |
| data.hit_bug_data.crash_count | string | 崩溃次数 |
| data.hit_bug_data.error_count | string | 错误次数 |
| data.hit_bug_data.start_dev_num | string | 启动设备数 |
| data.hit_bug_data.crash_dev_num | string | 崩溃设备数 |
| data.hit_bug_data.error_dev_num | string | 错误设备数 |
| data.perf_mate_data | object | 性能监控数据          |
| data.perf_mate_data.smoothness | string | 卡顿率 |
| data.perf_mate_data.avg_memory | string | 平均内存使用（MB） |
| data.perf_mate_data.avg_network_traffic | string | 平均网络流量（KB） |
| data.perf_mate_data.avg_network_delay | string | 平均网络延迟（ms） |
| data.perf_mate_data.avg_battery_power | string | 平均电池电量（%） |
| data.perf_mate_data.avg_battery_temp | string | 平均电池温度（℃） |
| data.perf_mate_data.avg_fps_power | string | 每帧功耗（mW） |
| data.sdk_data | object | SDK事件数据           |
| data.sdk_data.*.event_name | string | 事件名称 |
| data.sdk_data.*.count | string | 事件发生次数 |
| data.sdk_data.*.avg_value | string | 平均值 |
| data.sdk_data.*.sum_value | string | 总值 |
| data.sdk_rate | object | SDK关键性能指标       |
| data.sdk_rate.average_startup_time | string | 平均启动时间（秒） |
| data.sdk_rate.app_launch_error_rate | string | 应用启动错误率 |
| data.sdk_rate.hot_update_time | string | 热更新时间（秒） |
| data.sdk_rate.hot_update_error_rate | string | 热更新错误率 |
| data.sdk_rate.sdk_init_error_rate | string | SDK初始化错误率 |

#### 请求示例

```bash
curl -X GET "https://sdk-monitor-dashboard.shiyue.com/api/dashboard" \
  -H "Content-Type: application/json" \
  -G \
  -d "developer_app_id=6" \
  -d "start_time=2025-05-29 00:00:00" \
  -d "end_time=2025-05-30 00:00:00"
```

#### 响应示例

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "inner_version": [
      {
        "version": "202410261816",
        "app_launch_error_rate": "0.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      },
      {
        "version": "2025052313",
        "app_launch_error_rate": "75.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      }
    ],
    "app_version": [
      {
        "version": "1.1.1",
        "app_launch_error_rate": "0.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      }
    ],
    "hit_bug_data": {
      "crash": "0.00",
      "error": "0.00",
      "start_count": "0",
      "crash_count": "0",
      "error_count": "0",
      "start_dev_num": "0",
      "crash_dev_num": "0",
      "error_dev_num": "0"
    },
    "perf_mate_data": {
      "smoothness": "0",
      "avg_memory": "0",
      "avg_network_traffic": "0",
      "avg_network_delay": "0",
      "avg_battery_power": "0",
      "avg_battery_temp": "0",
      "avg_fps_power": "0"
    },
    "sdk_data": {
      "app_launch_error": {
        "event_name": "app_launch_error",
        "count": "6",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "sdk_init_success": {
        "event_name": "sdk_init_success",
        "count": "15",
        "avg_value": "1.00",
        "sum_value": "14"
      }
    },
    "sdk_rate": {
      "average_startup_time": "0.00",
      "app_launch_error_rate": "100.00",
      "hot_update_time": "3.00",
      "hot_update_error_rate": "100.00",
      "sdk_init_error_rate": "28.57"
    }
  }
}
```

## 错误响应

### 参数验证失败

```json
{
  "code": 422,
  "message": "验证失败：开发者应用ID必须是整数",
  "data": null
}
```

### 时间参数错误

```json
{
  "code": 422,
  "message": "验证失败：结束时间必须晚于开始时间",
  "data": null
}
```

### 服务器错误

```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null
}
```

## 数据字段详细说明

### SDK事件类型说明

`sdk_data` 对象包含多种 SDK 事件类型，常见的事件包括：

- `app_launch_error`: 应用启动错误
- `app_launch_screen_completion`: 应用启动屏幕完成
- `come_in_game_error`: 进入游戏错误
- `default_server_request_error`: 默认服务器请求错误
- `download_error`: 下载错误
- `download_status`: 下载状态
- `download_time`: 下载时间
- `hot_update_cdn_error`: 热更新CDN错误
- `hot_update_error`: 热更新错误
- `hot_update_time_exception`: 热更新时间异常
- `login_game_error`: 登录游戏错误
- `login_time`: 登录时间
- `login_to_main_screen_time`: 登录到主界面时间
- `network_disconnection`: 网络断开
- `remote_url_error`: 远程URL错误
- `sdk_get_order_error`: SDK获取订单错误
- `sdk_get_order_success`: SDK获取订单成功
- `sdk_init_error`: SDK初始化错误
- `sdk_init_success`: SDK初始化成功
- `sdk_login_error`: SDK登录错误
- `sdk_login_success`: SDK登录成功
- `sdk_pay_page_time`: SDK支付页面时间
- `sence_change_time`: 场景切换时间
- `server_request_error`: 服务器请求错误
- `socket_request_error`: Socket请求错误
- `ui_open_time`: UI打开时间

### SDK比率指标说明

`sdk_rate` 对象包含的关键性能指标：

- `average_startup_time`: 平均启动时间（秒）
- `app_launch_error_rate`: 应用启动错误率（%）
- `hot_update_time`: 热更新时间（秒）
- `hot_update_error_rate`: 热更新错误率（%）
- `sdk_init_error_rate`: SDK初始化错误率（%）
- `sdk_login_error_rate`: SDK登录错误率（%）
- `default_server_request_error_rate`: 默认服务器请求错误率（%）
- `server_request_error_rate`: 服务器请求错误率（%）
- `login_game_error_rate`: 登录游戏错误率（%）
- `come_in_game_error_rate`: 进入游戏错误率（%）
- `login_to_main_screen_time`: 登录到主界面时间（秒）
- `download_error_rate`: 下载错误率（%）
- `ui_open_time_rate`: UI打开时间比率
- `scene_change_time_rate`: 场景切换时间比率
- `network_disconnection_rate`: 网络断开率（%）

## 缓存机制

系统内置了10分钟的数据缓存机制，相同参数的请求会优先返回缓存数据，提高响应速度。缓存键基于开发者应用ID、开始时间和结束时间生成。

## 注意事项

1. 所有时间参数必须使用 `Y-m-d H:i:s` 格式
2. 结束时间必须晚于开始时间
3. 开发者应用ID必须是大于0的整数
4. 所有数值字段都会被格式化为字符串类型，数值保留两位小数
5. 内部版本号采用时间戳格式（如：202410261816），应用版本号采用语义化版本格式（如：1.1.1）
6. 响应数据较大，建议在网络条件较差时显示加载提示
7. 系统支持缓存机制，相同参数的请求可能返回缓存数据
8. 响应遵循项目统一的API响应规范
